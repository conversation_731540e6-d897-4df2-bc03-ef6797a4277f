# TypeORM Migration 使用指南

本專案已從手動 migration 腳本改為使用 **TypeORM 官方 CLI** 工具。

## 🔄 主要變更

### 之前（手動腳本）
```bash
pnpm run create-migration <name>
pnpm run run-migration
pnpm run rollback-migration
```

### 現在（TypeORM CLI）
```bash
pnpm run migration:create src/migrations/files/<name>
pnpm run migration:run
pnpm run migration:revert
```

## 📋 可用指令

### 1. 建立新的 Migration
```bash
# 建立空白 migration 檔案
pnpm run migration:create src/migrations/files/add_user_table

# 根據 Entity 變更自動生成 migration
pnpm run migration:generate src/migrations/files/update_user_entity
```

### 2. 執行 Migration
```bash
# 執行所有待執行的 migration
pnpm run migration:run

# 顯示 migration 狀態
pnpm run migration:show
```

### 3. 回滾 Migration
```bash
# 回滾最後一個 migration
pnpm run migration:revert
```

### 4. Schema 管理（謹慎使用）
```bash
# 刪除所有資料表
pnpm run schema:drop

# 同步 schema（僅開發環境）
pnpm run schema:sync
```

## 🏗️ Migration 檔案結構

TypeORM CLI 生成的 migration 檔案格式：

```typescript
import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserTable1750232944733 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // 執行變更的邏輯
        await queryRunner.query(`
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滾變更的邏輯
        await queryRunner.query(`DROP TABLE users`);
    }
}
```

## 🔧 設定檔案

- **DataSource 設定**: `src/data-source.ts`
- **TypeScript 設定**: `tsconfig.typeorm.json`
- **Migration 檔案位置**: `src/migrations/files/`

## 🎯 Migration 執行模式

本專案設定為 **純 Migration Files 模式**，具有以下特點：

- ✅ **只依照 migration files 執行**，不管 entities 的變更
- ✅ **完全手動控制** 資料庫 schema 變更
- ✅ **避免意外的 schema 同步**
- ✅ **適合生產環境** 的穩定部署

### 與 Entity-based Migration 的差異

| 模式 | 優點 | 缺點 |
|------|------|------|
| **Migration Files Only** (本專案) | 完全控制、穩定、適合生產環境 | 需要手動維護 SQL |
| **Entity-based** | 自動生成、開發快速 | 可能產生意外變更 |

## 💡 最佳實踐

1. **命名規範**: 使用描述性的名稱，如 `add_user_table`、`update_product_schema`
2. **原子性**: 每個 migration 應該是原子性的，要麼全部成功，要麼全部失敗
3. **可回滾**: 總是實作 `down` 方法來支援回滾
4. **測試**: 在開發環境中測試 migration 的執行和回滾
5. **手動 SQL**: 仔細撰寫 SQL，確保與現有 schema 相容

## 🚨 注意事項

- 生產環境執行 migration 前請先備份資料庫
- 避免在生產環境使用 `schema:sync`
- Migration 檔案一旦提交到版本控制，不應該再修改
