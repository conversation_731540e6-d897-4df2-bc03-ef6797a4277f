import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from '../../config/databaseConfig'

// 建立 DataSource 實例（可共用）
const AppDataSource = new DataSource({
  type: 'mysql',
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
  synchronize: false,
  logging: false,
  migrations: ['src/migrations/files/*.ts'],
})

const rollbackMigration = async (): Promise<void> => {
  try {
    console.log('⏳ Initializing database connection...')
    await AppDataSource.initialize()

    console.log('⚙️ Reverting the last migration...')
    await AppDataSource.undoLastMigration()

    console.log('✅ Migration reverted successfully!')
  } catch (error) {
    console.error('❌ Error reverting migration:', (error as Error).message)
    process.exit(1)
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy()
      console.log('🔌 Database connection closed.')
    }
  }
}

rollbackMigration()
