import path from 'path'
import fs from 'fs/promises'

// 定義遷移檔案目錄
const MIGRATION_DIR = path.resolve('src/migrations/files')

const ensureDir = async (dirPath: string) => {
  await fs.mkdir(dirPath, { recursive: true })
}

/**
 * 生成新的遷移檔案，檔名基於時間戳格式
 * @param migrationName - 遷移檔案的名稱
 */
const generateMigration = async (migrationName: string) => {
  try {
    const timestamp = Date.now()
    const formattedName = `${timestamp}_${migrationName}`
    const migrationPath = path.join(MIGRATION_DIR, `${formattedName}.ts`)

    const template = `import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName}${timestamp} implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 在這裡添加遷移邏輯
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 在這裡添加回滾邏輯
  }
}
`

    await ensureDir(MIGRATION_DIR)
    await fs.writeFile(migrationPath, template.trim())
    console.log(`✅ 遷移檔案已建立於: ${migrationPath}`)
  } catch (error) {
    console.error(`❌ 建立遷移檔案時發生錯誤: ${(error as Error).message}`)
    process.exit(1)
  }
}

// 從命令列參數獲取遷移名稱
const args = process.argv.slice(2)

if (args.length < 1) {
  console.error('❌ 請提供遷移名稱。')
  console.log('用法: ts-node createMigrationFile.ts <遷移名稱>')
  process.exit(1)
}

// 執行遷移檔案生成
generateMigration(args[0])
