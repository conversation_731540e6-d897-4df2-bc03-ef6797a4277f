# 舊版 Migration 腳本備份

這個資料夾包含了之前使用的手動 migration 腳本，已被 TypeORM 官方 CLI 取代。

## 備份檔案

- `createMigrationFile.ts` - 舊版建立 migration 檔案的腳本
- `runMigration.ts` - 舊版執行 migration 的腳本  
- `rollbackMigration.ts` - 舊版回滾 migration 的腳本

## 新版指令

請使用以下新的 TypeORM CLI 指令：

```bash
# 建立 migration
pnpm run migration:create src/migrations/files/<name>

# 執行 migration
pnpm run migration:run

# 回滾 migration
pnpm run migration:revert
```

詳細說明請參考：`docs/migration-guide.md`
