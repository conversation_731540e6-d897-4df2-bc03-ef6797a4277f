import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from '../../config/databaseConfig'

// 建立 DataSource 實例（可共用）
const AppDataSource = new DataSource({
  type: 'mysql',
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
  synchronize: false,
  logging: false,
  migrations: ['src/migrations/files/*.ts'],
  connectTimeout: 60000, // 增加超時時間
  extra: {
    enableKeepAlive: true,
    keepAliveInitialDelay: 300000, // 5 分鐘後啟動 keep-alive
  },
})

const runMigrations = async (): Promise<void> => {
  try {
    console.log('⏳ Initializing database connection...')
    await AppDataSource.initialize()

    console.log('⚙️ Running pending migrations...')
    const results = await AppDataSource.runMigrations()

    results.forEach((migration) => {
      console.log(`✅ Applied migration: ${migration.name}`)
    })

    console.log('🎉 All migrations applied successfully!')
  } catch (error) {
    console.log('error', error)
    console.error('❌ Error running migrations:', (error as Error).message)
    process.exit(1)
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy()
      console.log('🔌 Database connection closed.')
    }
  }
}

runMigrations()
