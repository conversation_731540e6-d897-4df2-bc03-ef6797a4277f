import { MigrationInterface, QueryRunner, Table } from 'typeorm'

export class create_sub_accounts_table1740453603664
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('sub_accounts')
    if (!exists) {
      await queryRunner.createTable(
        new Table({
          name: 'sub_accounts',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: false,
            },
            {
              name: 'is_enable',
              type: 'boolean',
              default: true,
            },
            {
              name: 'main_account_id',
              type: 'int',
              isNullable: false,
            },
            {
              name: 'application_form_id',
              type: 'int',
              isNullable: false,
            },
            {
              name: 'is_debit', // true 表示借方（加項），false 表示貸方（減項）
              type: 'boolean',
              default: true, // 預設為借方
              comment:
                'True for debit (addition), false for credit (subtraction)',
            },
            {
              name: 'created_at',
              type: 'datetime',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'datetime',
              default: 'CURRENT_TIMESTAMP',
              onUpdate: 'CURRENT_TIMESTAMP',
            },
          ],
        }),
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('sub_accounts')
    if (exists) {
      await queryRunner.dropTable('sub_accounts')
    }
  }
}
