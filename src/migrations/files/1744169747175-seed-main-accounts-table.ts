import { MigrationInterface, QueryRunner } from 'typeorm'

export class seed_main_accounts_table1744169747175
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO main_accounts (name) VALUES
      ('營業收入'),
      ('營業成本')
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM main_accounts WHERE name IN (
        '營業收入',
        '營業成本'
      )
    `)
  }
}
