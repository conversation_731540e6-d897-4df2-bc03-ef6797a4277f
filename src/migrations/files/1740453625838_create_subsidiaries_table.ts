import { MigrationInterface, QueryRunner, Table } from 'typeorm'

export class create_subsidiaries_table1740453625838
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('subsidiaries')
    if (!exists) {
      await queryRunner.createTable(
        new Table({
          name: 'subsidiaries',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: false,
            },
            {
              name: 'is_enable',
              type: 'boolean',
              default: true,
            },
          ],
        }),
      )

      await queryRunner.query(
        `INSERT INTO subsidiaries (name) VALUES 
      ('果實夥伴股份有限公司'), 
      ('果然行銷股份有限公司'), 
      ('果效數位股份有限公司')`,
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('subsidiaries')
    if (exists) {
      await queryRunner.dropTable('subsidiaries')
    }
  }
}
