import { MigrationInterface, QueryRunner, Table } from 'typeorm'

export class create_financial_records_table1740453636343
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'financial_records',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'product_category_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'one_campaign_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'pre_campaign_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'pre_campaign_detail_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'subsidiary_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'transaction_type', // 收款還是付款
            type: 'enum',
            enum: ['INCOME', 'EXPENSE'],
            isNullable: false,
            comment: 'INCOME for receipt, EXPENSE for payment',
          },
          {
            name: 'counterparty_entity_type', // 自然人還是法人
            type: 'enum',
            enum: ['PERSON', 'COMPANY'],
            isNullable: false,
            comment: 'PERSON for individual, COMPANY for legal entity',
          },
          {
            name: 'counterparty_role', // 在資料庫中的類別
            type: 'enum',
            enum: ['CLIENT', 'MEDIA', 'AGENCY'],
            isNullable: true,
            comment: 'Role of the counterparty in our database',
          },
          {
            name: 'counterparty_id', // 該類別的id
            type: 'int',
            isNullable: true,
            comment: 'ID of the counterparty in the specified category',
          },
          {
            name: 'sub_account_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'date',
            type: 'date',
            isNullable: false,
          },
          {
            name: 'currency_code',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'exchange_rate',
            type: 'decimal',
            precision: 15,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'adjusted_exchange_rate',
            type: 'decimal',
            precision: 15,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'adjusted_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'TWD_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'adjusted_TWD_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'accrual_voucher_number',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'Voucher number at the time of accrual estimation',
          },
          {
            name: 'actual_voucher_number',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment:
              'Voucher number at the time of actual recognition (when invoice arrives)',
          },
          {
            name: 'invoice_number',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'Invoice number',
          },
          {
            name: 'uniform_invoice_number',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'Taiwan invoice number',
          },
          {
            name: 'invoice_date',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'note',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'is_locked',
            type: 'boolean',
            default: false,
          },
          {
            name: 'is_deleted',
            type: 'boolean',
            default: false,
          },
          {
            name: 'creator_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('financial_records')
  }
}
