import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm'

export class addColumnForCounterparty1750227385765
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE counterparties
      ADD COLUMN source_table VARCHAR(100) NOT NULL DEFAULT 'financial_counterparties' AFTER id;
    `)

    await queryRunner.query(`
      ALTER TABLE counterparties
      ADD COLUMN source_id INT NULL AFTER source_table;
    `)

    await queryRunner.createIndex(
      'counterparties',
      new TableIndex({
        name: 'IDX_counterparties_source',
        columnNames: ['source_table', 'source_id'],
        isUnique: true,
      }),
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('counterparties', 'IDX_counterparties_source')
    await queryRunner.query(`ALTER TABLE counterparties DROP COLUMN source_id;`)
    await queryRunner.query(
      `ALTER TABLE counterparties DROP COLUMN source_table;`,
    )
  }
}
