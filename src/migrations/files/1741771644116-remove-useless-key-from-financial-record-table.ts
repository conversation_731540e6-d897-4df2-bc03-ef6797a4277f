import { MigrationInterface, QueryRunner } from 'typeorm'

export class remove_useless_key_from_financial_record_table1741771644116
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('financial_records')

    if (table?.findColumnByName('product_category_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records DROP COLUMN product_category_id`,
      )
    }

    if (table?.findColumnByName('one_campaign_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records DROP COLUMN one_campaign_id`,
      )
    }

    if (table?.findColumnByName('pre_campaign_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records DROP COLUMN pre_campaign_id`,
      )
    }

    if (table?.findColumnByName('pre_campaign_detail_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records DROP COLUMN pre_campaign_detail_id`,
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('financial_records')

    if (!table?.findColumnByName('product_category_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records ADD COLUMN product_category_id int NULL`,
      )
    }

    if (!table?.findColumnByName('one_campaign_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records ADD COLUMN one_campaign_id int NULL`,
      )
    }

    if (!table?.findColumnByName('pre_campaign_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records ADD COLUMN pre_campaign_id int NULL`,
      )
    }

    if (!table?.findColumnByName('pre_campaign_detail_id')) {
      await queryRunner.query(
        `ALTER TABLE financial_records ADD COLUMN pre_campaign_detail_id int NULL`,
      )
    }
  }
}
