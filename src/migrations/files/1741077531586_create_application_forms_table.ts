import { MigrationInterface, QueryRunner, Table } from 'typeorm'
export class create_application_forms_table1741077531586
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('application_forms')
    if (!exists) {
      await queryRunner.createTable(
        new Table({
          name: 'application_forms',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: false,
            },
            {
              name: 'is_enable',
              type: 'boolean',
              default: true,
            },
          ],
        }),
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('application_forms')
    if (exists) {
      await queryRunner.dropTable('application_forms')
    }
  }
}
