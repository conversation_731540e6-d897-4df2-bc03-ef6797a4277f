import { MigrationInterface, QueryRunner, Table } from 'typeorm'

export class createCounterpartiesTable1742805426118
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('counterparties')
    if (!exists) {
      await queryRunner.createTable(
        new Table({
          name: 'counterparties',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'type',
              type: 'enum',
              enum: [
                'DOMESTIC_COMPANY',
                'DOMESTIC_PERSON',
                'FOREIGN_COMPANY',
                'FOREIGN_PERSON',
              ],
            },
            {
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: false,
            },
            {
              name: 'identity_number',
              type: 'varchar',
              length: '255',
              isNullable: true,
            },
            {
              name: 'address',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'is_enable',
              type: 'boolean',
              default: true,
            },
            {
              name: 'created_at',
              type: 'datetime',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'datetime',
              default: 'CURRENT_TIMESTAMP',
              onUpdate: 'CURRENT_TIMESTAMP',
            },
          ],
        }),
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('counterparties')
    if (exists) {
      await queryRunner.dropTable('counterparties')
    }
  }
}
