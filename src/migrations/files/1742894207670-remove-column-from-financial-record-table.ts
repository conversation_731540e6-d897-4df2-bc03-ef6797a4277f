import { MigrationInterface, QueryRunner } from 'typeorm'
export class remove_column_from_financial_record_table1742894207670
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 移除不需要的欄位
    await queryRunner.query(
      `ALTER TABLE financial_records DROP COLUMN counterparty_entity_type`,
    )
    await queryRunner.query(
      `ALTER TABLE financial_records DROP COLUMN counterparty_role`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 還原移除的欄位
    await queryRunner.query(`
      ALTER TABLE financial_records 
      ADD COLUMN counterparty_entity_type ENUM('PERSON', 'COMPANY') NOT NULL COMMENT 'PERSON for individual, COMPANY for legal entity'
    `)

    await queryRunner.query(`
      ALTER TABLE financial_records 
      ADD COLUMN counterparty_role ENUM('CLIENT', 'MEDIA', 'AGENCY') NULL COMMENT 'Role of the counterparty in our database'
    `)
  }
}
