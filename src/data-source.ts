import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from './config/databaseConfig'

const AppDataSource = new DataSource({
  type: 'mysql',
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
  synchronize: false,
  logging: false,
  // 移除 entities，只使用 migration files
  entities: [],
  migrations: ['src/migrations/files/*.ts'],
  migrationsTableName: 'migrations',
  connectTimeout: 60000,
  extra: {
    enableKeepAlive: true,
    keepAliveInitialDelay: 300000, // 5 分鐘後啟動 keep-alive
  },
})

export default AppDataSource
