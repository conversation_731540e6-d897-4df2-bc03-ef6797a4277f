import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from './config/databaseConfig'

const AppDataSource = new DataSource({
  type: 'mysql',
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
  synchronize: false,
  logging: false,
  entities: ['src/modules/**/database/typeorm/*.ts'],
  migrations: ['src/migrations/files/*.ts'],
  migrationsTableName: 'migrations',
  connectTimeout: 60000,
  extra: {
    enableKeepAlive: true,
    keepAliveInitialDelay: 300000, // 5 分鐘後啟動 keep-alive
  },
})

export default AppDataSource
