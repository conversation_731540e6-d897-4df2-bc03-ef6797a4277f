import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from './config/databaseConfig'

const AppDataSource = new DataSource({
  type: 'mysql',
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
  synchronize: false,
  logging: false,
  // 移除 entities，只使用 migration files
  entities: [],
  migrations: ['src/migrations/files/*.ts'],
  migrationsTableName: 'migrations',
  // 簡化連線設定，避免連線問題
  connectTimeout: 10000, // 10 秒連線超時
  extra: {
    // 最基本的設定
    connectionLimit: 1, // 單一連線，避免連線池問題
    ssl: false, // 關閉 SSL
  },
})

export default AppDataSource
