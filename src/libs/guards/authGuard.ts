import { truncateSync } from 'fs'
import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common'
import { truncate } from 'fs/promises'
import { IAuthService } from '../externalServices/auth'

export interface RequestWithUser extends Request {
  userId?: string
}
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    @Inject('IAuthService') private readonly authService: IAuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    console.log('AuthGuard canActivate called')
    const request = context.switchToHttp().getRequest<RequestWithUser>()
    const authHeader = request.headers['authorization'] as string
    const serviceApiKey = request.headers['service-api-key'] as string
    if (!authHeader && !serviceApiKey) {
      throw new UnauthorizedException('Invalid accessToken or Service-API-Key.')
    }

    if (authHeader) {
      const token = authHeader.split(' ')[1]
      const validationResult = await this.authService.validateUserToken(token)

      if (!validationResult.isValid) {
        throw new UnauthorizedException(
          validationResult.message || 'Unauthorized User',
        )
      }
      request.userId = await validationResult.userId?.toString()
    }

    if (serviceApiKey && serviceApiKey !== 'eji3g6cji3104') {
      throw new UnauthorizedException('Unauthorized Service-API-Key')
    }

    return true
  }
}
