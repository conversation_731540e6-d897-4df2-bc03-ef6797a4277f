import { ApiProperty } from '@nestjs/swagger'
import { FinancialRecordDetailResponseDto } from './financialRecordDetailResponseDto'

export class FinancialRecordApiResponseDto {
  @ApiProperty({
    description: '回應資料',
    type: 'object',
    properties: {
      totalCounts: {
        type: 'number',
        example: 95,
        description: '資料總筆數'
      },
      pageCounts: {
        type: 'number', 
        example: 4,
        description: '總筆數換算成的總頁數'
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: '當前哪一頁'
      },
      itemCounts: {
        type: 'number',
        example: 30,
        description: '當前頁面的筆數'
      },
      data: {
        type: 'array',
        items: { $ref: '#/components/schemas/FinancialRecordDetailResponseDto' },
        description: '財務記錄資料'
      }
    }
  })
  readonly data: {
    totalCounts: number
    pageCounts: number
    currentPage: number
    itemCounts: number
    data: FinancialRecordDetailResponseDto[]
  }

  constructor(props: {
    totalCounts: number
    pageCounts: number
    currentPage: number
    itemCounts: number
    data: FinancialRecordDetailResponseDto[]
  }) {
    this.data = props
  }
}
