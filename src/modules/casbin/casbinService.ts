import { join } from 'path'
import { Injectable, OnModuleInit } from '@nestjs/common'
import { Enforcer, newEnforcer } from 'casbin'
import TypeORMAdapter from 'typeorm-adapter'
import { databaseConfig } from '../../config/databaseConfig'

@Injectable()
export class CasbinService implements OnModuleInit {
  private enforcer: Enforcer

  async onModuleInit() {
    const adapter = await TypeORMAdapter.newAdapter({
      type: 'mysql',
      host: databaseConfig.host,
      port: databaseConfig.port,
      username: databaseConfig.username,
      password: databaseConfig.password,
      database: databaseConfig.database,
    })

    this.enforcer = await newEnforcer(
      join(process.cwd(), 'src/modules/casbin/model.conf'),
      adapter,
    )
    await this.enforcer.loadPolicy()
  }

  getEnforcer(): Enforcer {
    return this.enforcer
  }

  // async checkPermission(sub: any, obj: any, act: string): Promise<boolean> {
  //   return await this.enforcer.enforce(sub, obj, act)
  // }

  // async getUserDepartments(userId: string): Promise<string[]> {
  //   const allG2Ploicys = await this.enforcer.getNamedGroupingPolicy('g2')
  //   const departments = allG2Ploicys
  //     .filter(([user, _]) => user === userId)
  //     .map(([_, dept]) => dept)
  //   return departments
  // }
}
