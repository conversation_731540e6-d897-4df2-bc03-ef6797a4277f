import { Injectable } from '@nestjs/common'
import { CasbinService } from '../casbin/casbinService'

@Injectable()
export class PermissionService {
  constructor(private readonly casbinService: CasbinService) {}

  async getAccessibleFormIds(userId: string, act?: string): Promise<any> {
    const subKey = `user:${userId}`

    const roles = await this.casbinService.getEnforcer().getRolesForUser(subKey)
    console.log('roles', roles)
    const depts = (
      await this.casbinService.getEnforcer().getNamedGroupingPolicy('g2')
    )
      .filter(([u]) => u === subKey)
      .map(([_, dept]) => dept)

    const allSubs = [...roles, subKey]
    const policies = await this.casbinService.getEnforcer().getPolicy()

    const allowedFormIds = policies
      .filter(([pSub, pDept, formId, pAct]) => {
        const matchAct = act ? pAct === act : true
        return allSubs.includes(pSub) && depts.includes(pDept) && matchAct
      })
      .map(([_, __, formId]) => formId)

    return {
      roles,
      depts,
      applicationForms: [...new Set(allowedFormIds)],
    }
  }
}
