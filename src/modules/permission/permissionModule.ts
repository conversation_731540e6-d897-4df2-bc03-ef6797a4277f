import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { CasbinModule } from '../casbin/casbinModule'
import { PermissionService } from './permissionService'
import { FindUserPermissionController } from './queries/find-user-permissions/findUserPermissionsHttpController'
import { FindUserPermissionsQueryHandler } from './queries/find-user-permissions/findUserPermissionsQueryHandler'

@Module({
  imports: [CqrsModule, CasbinModule],
  providers: [PermissionService, FindUserPermissionsQueryHandler],
  controllers: [FindUserPermissionController],
})
export class PermissionModule {}
