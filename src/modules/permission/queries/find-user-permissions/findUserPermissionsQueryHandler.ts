import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs'
import { UserPermissionResponseDto } from '../../dtos/userPermissionResponseDto'
import { PermissionService } from '../../permissionService'
import { FindUserPermissionsQuery } from './findUserPermissionsQuery'

@QueryHandler(FindUserPermissionsQuery)
export class FindUserPermissionsQueryHandler
  implements IQueryHandler<FindUserPermissionsQuery>
{
  constructor(private readonly permissionService: PermissionService) {}

  async execute(
    query: FindUserPermissionsQuery,
  ): Promise<UserPermissionResponseDto> {
    const data = await this.permissionService.getAccessibleFormIds(
      query.userId,
      query.act,
    )
    return {
      userId: query.userId,
      ...data,
    }
  }
}
