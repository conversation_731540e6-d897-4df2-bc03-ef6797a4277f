# Finance System

本專案採用 **Domain-Driven Design (DDD)** 結合 **NestJS 架構**，以模組化、分層方式設計，強調領域邏輯與可維護性，並使用 **Node.js + TypeScript** 技術棧。

---

## 📁 專案結構說明

### 根目錄

- `Dockerfile`：定義應用的容器映像。
- `docker-compose.yml`：定義多容器（如資料庫）設定。
- `.env`：環境變數檔。
- `env.example`：環境變數範本。
- `package.json`：Node.js 套件與指令管理。
- `tsconfig.json` / `tsconfig.build.json`：TypeScript 編譯器設定。
- `nest-cli.json`：NestJS 專案設定。
- `biome.json`：程式碼格式化與靜態檢查設定。
- `pnpm-lock.yaml`：PNPM 套件鎖定檔案。

---

### 主要資料夾

#### `.vscode/`
- VS Code 編輯器相關設定。

#### `src/`
主程式碼目錄，包含各功能模組與共用資源。
- `app.module.ts` / `main.ts`：應用啟動入口。
- `config/`：應用程式設定（路由、資料庫、外部服務等）。
- `libs/`：共用函式庫與工具（API 回應、應用層、DB、DDD、裝飾器、型別、工具等）。
- `migrations/`：
  - `files/`：資料庫遷移檔案。
  - `scripts/`：遷移指令腳本。
  - `erDiagram.mmd`：資料庫ER圖
- `modules/`：各功能模組（如 accounting、applicationForm、counterparty、financialRecord、subsidiary）。

#### `tests/`
- `unit/`：單元測試。
  - `setup.ts`：測試初始化。
  - `modules/`：模組化測試資料夾。

---

## 📦 使用套件簡介

| 套件名稱           | 功能簡介                                 |
| ------------------ | ---------------------------------------- |
| `dotenv`           | 載入 `.env` 檔案中的環境變數。           |
| `fastify`          | 高效能 Node.js Web 框架。                |
| `mysql2`           | MySQL 資料庫連線工具，支援 Promise。     |
| `reflect-metadata` | 提供 TypeScript 裝飾器所需的元資料功能。 |
| `typeorm`          | TypeScript 專用 ORM 工具。               |
| `jest`             | 單元測試框架。                           |
| `biome`            | 程式碼格式化與靜態檢查工具。             |
| `nestjs`           | Node.js 應用框架。                      |
| `pnpm`             | 套件管理工具。                           |

---

## 🛠️ 資料庫遷移指令

本專案已改用 **TypeORM 官方 CLI** 工具來管理資料庫遷移：

```bash
# 建立新的遷移檔案（空白模板）
pnpm run migration:create src/migrations/files/<migration_name>

# 根據 Entity 變更自動生成遷移檔案
pnpm run migration:generate src/migrations/files/<migration_name>

# 執行所有待執行的遷移
pnpm run migration:run

# 回滾上一個遷移
pnpm run migration:revert

# 顯示所有遷移狀態
pnpm run migration:show

# 其他實用指令
pnpm run schema:drop    # 刪除所有資料表
pnpm run schema:sync    # 同步 schema（僅開發環境使用）
```

### 遷移檔案管理
- 遷移檔案位置：`src/migrations/files/`
- TypeORM 設定檔：`src/data-source.ts`
- 遷移記錄表：`migrations`

---

## 🐳 容器化操作（Docker）

預設容器使用 Port：`3100`

```bash
# 建立 Docker 映像
docker build -t finance_system_backend .

# 啟動容器
docker-compose up -d

# 停止容器
docker-compose down
```

---

## 🧑‍💻 本機端開發準備

1. 安裝本機 MySQL。
2. 匯入資料庫（預設名稱：`gspadmin`）。
3. 執行一次資料庫遷移。
4. 複製 `env.example` 並命名為 `.env`（容器化環境可跳過）。

---

## 🚀 啟動開發伺服器

```bash
# 安裝依賴
pnpm install

# 啟動伺服器
pnpm run start:dev
```

---

## 🧹 程式碼檢查與格式化

專案已整合 Biome 工具進行程式碼靜態檢查與自動格式化，建議於開發過程中定期執行。

```bash
# 執行 lint 檢查
pnpm run lint

# 自動修正可修正的 lint 問題
pnpm run check

# 執行程式碼格式化
pnpm run format
```

---
