{"name": "finance-system", "version": "1.0.0", "description": "此專案採用 **Domain-Driven Design (DDD)** 架構。", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "format": "biome format --write --config-path=./biome.json . || true", "lint": "biome lint . || true", "check": "biome check --write --config-path=./biome.json . || true", "test": "jest --config .jestrc.json", "test:cov": "jest --config .jestrc.json --coverage", "test:watch": "jest --watch", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "migration:create": "typeorm-ts-node-commonjs migration:create", "migration:run": "ts-node src/migration-runner.ts run", "migration:revert": "ts-node src/migration-runner.ts revert", "migration:show": "ts-node src/migration-runner.ts show"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/cqrs": "^11.0.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-fastify": "^11.0.20", "@nestjs/swagger": "^11.1.4", "@nestjs/typeorm": "^11.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "env-var": "^7.5.0", "jest-cucumber": "^4.5.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "mysql2": "^3.14.0", "nestjs-request-context": "^4.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.22", "typeorm-transactional-cls-hooked": "^0.1.21", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "globals": "^16.0.0", "jest": "^29.7.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@9.12.3"}